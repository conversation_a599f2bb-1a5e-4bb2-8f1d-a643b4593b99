# [0.5.0](https://github.com/ClashStrategic/webapp/compare/v0.4.3...v0.5.0) (2025-05-26)


### Features

* **sw:** store service worker (webapp) version and build datetime ([0cbc3a1](https://github.com/ClashStrategic/webapp/commit/0cbc3a14831fdf7c8d52adb188ba65ccdf551a93))

## [0.4.3](https://github.com/ClashStrategic/webapp/compare/v0.4.2...v0.4.3) (2025-05-26)


### Bug Fixes

* **api:** remove version check from API response handling ([96bac2c](https://github.com/ClashStrategic/webapp/commit/96bac2cb84d6d596c466cbee7cc00f5c09e07ad3))
* **sw:** update service worker registration and caching logic ([e94c752](https://github.com/ClashStrategic/webapp/commit/e94c7525ef46bbc4dde8c2a70cfed126878bb15b))

## [0.4.2](https://github.com/ClashStrategic/webapp/compare/v0.4.1...v0.4.2) (2025-05-23)


### Bug Fixes

* **tooltip:** Activate fading by pressing outside the tooltip when it is in the general toggle ([0a1a3ce](https://github.com/ClashStrategic/webapp/commit/0a1a3ce2958f6fc1b1fbaf195f18b98ce0750f39))

## [0.4.1](https://github.com/ClashStrategic/webapp/compare/v0.4.0...v0.4.1) (2025-05-23)


### Bug Fixes

* **privacy:** add comprehensive privacy policy document ([52a99b1](https://github.com/ClashStrategic/webapp/commit/52a99b1ed7d8f4c7d5fae9d850f1a24069aa0a49))

# [0.4.0](https://github.com/ClashStrategic/webapp/compare/v0.3.0...v0.4.0) (2025-05-22)


### Bug Fixes

* **home:** store API base URL in localStorage for fetch ([3eb00a7](https://github.com/ClashStrategic/webapp/commit/3eb00a7e571abea0c8d1d4ba3932fb450e3f877c))


### Features

* Configure dynamic API base URL and fetch home content ([3800d67](https://github.com/ClashStrategic/webapp/commit/3800d6716d5364d9d5a1678876da72ae850ba079))

# [0.3.0](https://github.com/ClashStrategic/webapp/compare/v0.2.0...v0.3.0) (2025-05-21)


### Features

* **htaccess, error pages:** add server configuration and error pages ([ca4b0b8](https://github.com/ClashStrategic/webapp/commit/ca4b0b81c2f64672c9397c6f92c56a4353eb7ab7))

# [0.2.0](https://github.com/ClashStrategic/webapp/compare/v0.1.0...v0.2.0) (2025-05-21)


### Features

* add robots.txt for bot access control and sitemap.xml for better indexing ([e1cd5a6](https://github.com/ClashStrategic/webapp/commit/e1cd5a69ffec4481cc18f202b0e11ff54161d0d7))
